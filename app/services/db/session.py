import asyncio
from contextvars import <PERSON>textVar

from loguru import logger
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncEng<PERSON>, AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import AsyncAdaptedQ<PERSON>uePool, NullPool

from config.loader import BaseAppSettings, Environment, SettingsFactory

_SessionFactory: async_sessionmaker | None = None
_ctx_session: ContextVar[AsyncSession | None] = ContextVar("session", default=None)
_ctx_multi_sessions: ContextVar[bool] = ContextVar("multi_sessions", default=False)
_ctx_commit_on_exit: ContextVar[bool] = ContextVar("commit_on_exit", default=False)


def init_session_factory(
    db_url: str | URL,
    engine_args: dict | None = None,
    session_args: dict | None = None,
) -> async_sessionmaker:
    global _SessionFactory
    print(f"Initializing session factory with db_url: {db_url}")
    engine_args = engine_args or {}
    session_args = session_args or {}

    engine = create_async_engine(db_url, **engine_args)
    _SessionFactory = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False, **session_args)
    return _SessionFactory


class DBSession:
    def __init__(
        self,
        session_args: dict | None = None,
        commit_on_exit: bool = False,
        multi_sessions: bool = False,
    ):
        self.token = None
        self.session_args = session_args or {}
        self.commit_on_exit = commit_on_exit
        self.multi_sessions = multi_sessions

    async def __aenter__(self):
        if _SessionFactory is None:
            raise RuntimeError("DBSession not initialized. Call init_session_factory() first.")

        if self.multi_sessions:
            self.token_multi = _ctx_multi_sessions.set(True)
            self.token_commit = _ctx_commit_on_exit.set(self.commit_on_exit)
        else:
            session = _SessionFactory(**self.session_args)
            self.token = _ctx_session.set(session)
        return self

    async def __aexit__(self, exc_type, exc_val, tb):
        if self.multi_sessions:
            _ctx_multi_sessions.reset(self.token_multi)
            _ctx_commit_on_exit.reset(self.token_commit)
        else:
            session = _ctx_session.get()
            try:
                if exc_type:
                    await session.rollback()
                elif self.commit_on_exit:
                    await session.commit()
            finally:
                await session.close()
                _ctx_session.reset(self.token)

    @property
    def session(self) -> AsyncSession:
        if _SessionFactory is None:
            raise RuntimeError("DBSession not initialized")

        if _ctx_multi_sessions.get():
            session = _SessionFactory()
            task = asyncio.current_task()
            if task is not None:

                async def cleanup():
                    try:
                        if _ctx_commit_on_exit.get():
                            await session.commit()
                    except Exception:
                        await session.rollback()
                        raise
                    finally:
                        await session.close()

                task.add_done_callback(lambda t: asyncio.create_task(cleanup()))
            return session

        session = _ctx_session.get()
        if session is None:
            raise RuntimeError("No session in current context")
        return session


def new_session() -> AsyncSession:
    return DBSession().session


def get_engine_args(settings: BaseAppSettings) -> dict:
    # Use ENABLE_SQL_LOGS environment variable to control SQL logging
    echo = settings.ENABLE_SQL_LOGS
    pool_pre_ping = True
    pool_args = {"poolclass": NullPool}
    if settings.MODE != Environment.TEST:
        pool_args = {"poolclass": AsyncAdaptedQueuePool, "pool_size": settings.POOL_SIZE, "max_overflow": 64}
    return {
        "echo": echo,
        "pool_pre_ping": pool_pre_ping,
        **pool_args,
    }


class CommitBlockedSession(AsyncSession):
    """AsyncSession that blocks commit() calls"""

    async def commit(self):
        """Override commit to block it"""
        logger.warning("Session commits are handled automatically.")
        return await super().commit()

    async def force_commit(self):
        """Call the original commit method to bypass the block"""
        return await super().commit()


def get_pool_settings(settings: BaseAppSettings) -> dict:
    pool_args = {"poolclass": NullPool}
    if settings.MODE != Environment.TEST:
        pool_args = {"poolclass": AsyncAdaptedQueuePool, "pool_size": settings.POOL_SIZE, "max_overflow": 64}
    return pool_args


class SessionFactory:
    _engine = None
    _req_ctx_session_maker = None
    _local_session_maker = None

    @classmethod
    def engine(cls) -> AsyncEngine:
        if cls._engine is None:
            settings = SettingsFactory.settings()
            cls._engine = create_async_engine(
                url=str(settings.DATABASE_URI),
                echo=settings.ENABLE_SQL_LOGS,  # Use environment variable to control SQL logging
                pool_pre_ping=True,
                **get_pool_settings(settings),
            )
        return cls._engine

    @classmethod
    def req_ctx_session_maker(cls, bind=None):
        if cls._req_ctx_session_maker is None:
            cls._req_ctx_session_maker = async_sessionmaker(
                bind=bind or cls.engine(),
                class_=CommitBlockedSession,
                autoflush=True,
                autocommit=False,
                expire_on_commit=False,
            )
        return cls._req_ctx_session_maker

    @classmethod
    def local_session_maker(cls, bind=None):
        if cls._local_session_maker is None:
            cls._local_session_maker = async_sessionmaker(
                bind=bind or cls.engine(),
                class_=AsyncSession,
                autoflush=False,
                autocommit=False,
                expire_on_commit=False,
            )
        return cls._local_session_maker

    @classmethod
    def make_local_session(cls, bind=None) -> AsyncSession:
        """Create a local session with the default session maker."""
        session_maker = cls.local_session_maker(bind)
        return session_maker()

    @classmethod
    def make_req_ctx_session(cls, bind=None) -> CommitBlockedSession:
        """Create a request context session with the request context session maker."""
        session_maker = cls.req_ctx_session_maker(bind)
        return session_maker()

    @classmethod
    def reset(cls):
        cls._engine = None
        cls._req_ctx_session_maker = None
        cls._local_session_maker = None
