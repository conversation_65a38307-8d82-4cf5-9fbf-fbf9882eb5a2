import os

import firebase_admin
from firebase_admin import auth, credentials
from pydantic import BaseModel

# Initialize Firebase with error handling for development
firebase_app = None
try:
    if os.path.exists("config/secrets/firebase-admin.json"):
        cred = credentials.Certificate("config/secrets/firebase-admin.json")
        firebase_app = firebase_admin.initialize_app(cred)
        print("Firebase initialized successfully")
    else:
        print("Warning: Firebase credentials not found. Running in development mode without Firebase.")
except Exception as e:
    print(f"Warning: Failed to initialize Firebase: {e}")
    print("Running in development mode without Firebase authentication.")


class FirebaseIdentities(BaseModel):
    email: list[str]


class FirebaseInfo(BaseModel):
    identities: FirebaseIdentities
    sign_in_provider: str


class FirebaseUser(BaseModel):
    iss: str
    aud: str
    auth_time: int
    user_id: str
    sub: str
    iat: int
    exp: int
    email: str
    email_verified: bool
    firebase: FirebaseInfo
    uid: str


def verify_token(id_token: str) -> dict:
    return auth.verify_id_token(id_token)
