from enum import Enum

import stripe
from loguru import logger
from pydantic import BaseModel

from config.loader import SettingsFactory


class PaymentStatus(str, Enum):
    """Enum for Stripe checkout session payment status."""

    UNPAID = "unpaid"
    PAID = "paid"
    NO_PAYMENT_REQUIRED = "no_payment_required"


class CheckoutSessionStatus(str, Enum):
    """Enum for Stripe checkout session status."""

    OPEN = "open"
    COMPLETE = "complete"
    EXPIRED = "expired"


class StripeAPIError(Exception):
    """Custom exception for Stripe API errors."""

    def __init__(self, message: str = "Stripe API error occurred"):
        super().__init__(message)
        self.message = message


class StripeProduct(BaseModel):
    id: str
    name: str
    description: str | None = None
    metadata: dict = {}
    active: bool = True


class StripePrice(BaseModel):
    id: str
    product: str  # product ID
    nickname: str | None = None
    unit_amount: int  # amount in cents
    currency: str = "usd"
    active: bool = True


class StripeCheckoutSession(BaseModel):
    id: str
    payment_status: PaymentStatus
    status: CheckoutSessionStatus | None = None
    customer: str | None = None
    customer_email: str | None = None
    metadata: dict = {}
    url: str | None = None  # Checkout URL
    subscription: str | None = None  # Subscription ID if created


class StripeSubscription(BaseModel):
    id: str
    customer: str
    status: str
    latest_invoice: str | None = None
    metadata: dict = {}
    product_id: str


class StripeService:
    def __init__(self):
        settings = SettingsFactory.settings()
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.settings = settings

    async def get_all_products(self, limit: int = 100) -> list[StripeProduct]:
        """Fetch all products from Stripe."""
        try:
            products = []
            starting_after = None

            while True:
                params = {"limit": limit, "active": True}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Product.list(**params)

                for product in response.data:
                    products.append(
                        StripeProduct(
                            id=product.id,
                            name=product.name,
                            description=product.description,
                            metadata=product.metadata or {},
                            active=product.active,
                        )
                    )

                if not response.has_more:
                    break

                starting_after = response.data[-1].id

            logger.info(f"Fetched {len(products)} products from Stripe")

        except stripe.error.StripeError as e:
            logger.exception("Stripe API error while fetching products")
            raise StripeAPIError() from e
        except Exception as e:
            logger.exception("Unexpected error while fetching products")
            raise StripeAPIError() from e
        else:
            return products

    async def get_all_prices(self, limit: int = 100) -> list[StripePrice]:
        """Fetch all prices from Stripe."""
        try:
            prices = []
            starting_after = None

            while True:
                params = {"limit": limit, "active": True}
                if starting_after:
                    params["starting_after"] = starting_after

                response = stripe.Price.list(**params)

                for price in response.data:
                    prices.append(
                        StripePrice(
                            id=price.id,
                            product=price.product,
                            nickname=price.nickname,
                            unit_amount=price.unit_amount or 0,
                            currency=price.currency,
                            active=price.active,
                        )
                    )

                if not response.has_more:
                    break

                starting_after = response.data[-1].id

            logger.info(f"Fetched {len(prices)} prices from Stripe")

        except stripe.error.StripeError as e:
            logger.exception("Stripe API error while fetching prices")
            raise StripeAPIError() from e
        except Exception as e:
            logger.exception("Unexpected error while fetching prices")
            raise StripeAPIError() from e
        else:
            return prices

    async def create_checkout_session(
        self, *, price_id: str, customer_email: str, success_url: str, cancel_url: str
    ) -> StripeCheckoutSession:
        """Create a new checkout session."""
        try:
            params = {
                "line_items": [{"price": price_id, "quantity": 1}],
                "mode": "subscription",
                "success_url": success_url,
                "cancel_url": cancel_url,
                "customer_email": customer_email,
            }

            session = stripe.checkout.Session.create(**params)

            return StripeCheckoutSession(
                id=session.id,
                payment_status=session.payment_status,
                status=session.status,
                customer=session.customer,
                customer_email=session.customer_email,
                metadata=session.metadata or {},
                url=session.url,
                subscription=session.subscription,
            )

        except stripe.error.StripeError as e:
            logger.exception("Stripe API error while creating checkout session")
            raise StripeAPIError() from e
        except Exception as e:
            logger.exception("Unexpected error while creating checkout session")
            raise StripeAPIError() from e

    async def get_checkout_session(self, *, session_id: str) -> StripeCheckoutSession:
        """Retrieve a checkout session by ID."""
        try:
            session = stripe.checkout.Session.retrieve(session_id)
            logger.info(f"Fetched {session}")
            return StripeCheckoutSession(
                id=session.id,
                payment_status=session.payment_status,
                status=session.status,
                customer=session.customer,
                customer_email=session.customer_email,
                metadata=session.metadata or {},
                url=session.url,
                subscription=getattr(session, "subscription", None),
            )

        except stripe.error.StripeError as e:
            logger.exception("Stripe API error while retrieving checkout session")
            raise StripeAPIError() from e
        except Exception as e:
            logger.exception("Unexpected error while retrieving checkout session")
            raise StripeAPIError() from e

    async def get_subscription(self, *, subscription_id: str) -> StripeSubscription:
        """Retrieve a subscription by ID."""
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            logger.info(f"Fetched {subscription}")

            return StripeSubscription(
                id=subscription.id,
                customer=subscription.customer,
                status=subscription.status,
                latest_invoice=subscription.latest_invoice,
                metadata=subscription.metadata or {},
                product_id=subscription.plan.product,
            )

        except stripe.error.StripeError as e:
            logger.exception("Stripe API error while retrieving subscription")
            raise StripeAPIError() from e
        except Exception as e:
            logger.exception("Unexpected error while retrieving subscription")
            raise StripeAPIError() from e


# Singleton instance
stripe_service = StripeService()
