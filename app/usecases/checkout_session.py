import pendulum
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules import cruds
from app.modules.organization.subscription import IOrganizationSubscriptionCreate
from app.services.billing.client import stripe_service


async def process_checkout_session_completion(*, identity_id: str, session: AsyncSession) -> list[dict]:
    """
    Process Stripe checkout session completion and update internal records.

    This function:
    1. Retrieves open checkout sessions for the identity
    2. Updates checkout session records with latest status from Stripe
    3. If subscriptions were created, upserts the StripeSubscription table
    4. Returns subscription data for completed sessions

    Args:
        identity_id: The identity ID to process checkout sessions for
        session: Database session

    Returns:
        List of subscription data dictionaries for completed sessions

    Raises:
        StripeAPIError: If there's an error communicating with Stripe
        Exception: For other unexpected errors
    """
    open_checkout_session = await cruds.checkout_session_crud.get_by_status_and_identity_id(
        status="open", identity_id=identity_id, session=session
    )

    subscription_result = []

    for checkout_session in open_checkout_session:
        checkout_session_id = checkout_session.stripe_id
        logger.info(f"Processing checkout session completion for session {checkout_session_id}")

        # Retrieve checkout session from Stripe with subscription expansion
        stripe_checkout = await stripe_service.get_checkout_session(
            session_id=checkout_session_id,
        )

        logger.info(f"Retrieved Stripe checkout session: {stripe_checkout.id}, status: {stripe_checkout.status}")
        if stripe_checkout.status == "open":
            continue

        # Find the existing checkout session in our database
        existing_checkout = await cruds.checkout_session_crud.get_by_stripe_id(
            stripe_id=checkout_session_id, session=session
        )

        # Validate checkout session exists
        if not existing_checkout:
            logger.warning(f"Checkout session {checkout_session_id} not found in database")
            continue

        # Update the checkout session with latest data from Stripe
        existing_checkout.payment_status = stripe_checkout.payment_status
        existing_checkout.status = stripe_checkout.status
        session.add(existing_checkout)

        logger.info(f"Updated checkout session {existing_checkout.id} with status: {stripe_checkout.status}")

        # If a subscription was created during checkout, process it
        if stripe_checkout.subscription:
            logger.info(f"Processing subscription {stripe_checkout.subscription} from checkout session")

            # Retrieve full subscription data from Stripe
            stripe_subscription = await stripe_service.get_subscription(subscription_id=stripe_checkout.subscription)
            product = await cruds.product_crud.get_by_stripe_id(
                stripe_product_id=stripe_subscription.product_id, session=session
            )
            if product.stripe_metadata is None or product.stripe_metadata["code"] == "":
                continue
            code = product.stripe_metadata["code"]
            organization_subscription_id = await change_org_plan(
                organization_id=existing_checkout.organization_id, code=code, session=session
            )
            logger.info(f"{organization_subscription_id} org subscription id")
            # Prepare subscription data for upsert
            subscription_dict = {
                "id": stripe_subscription.id,
                "organization_subscription_id": organization_subscription_id,
                "status": stripe_subscription.status,
                "latest_invoice": stripe_subscription.latest_invoice,
                "identity_id": existing_checkout.identity_id,
                "organization_id": existing_checkout.organization_id,
            }

            # Upsert the subscription
            subscription_record = await cruds.stripe_subscription_crud.upsert_from_stripe(
                stripe_subscription=subscription_dict, session=session
            )

            logger.info(f"Upserted subscription {subscription_record.id} with Stripe ID {stripe_subscription.id}")

            subscription_data = {
                "id": subscription_record.id,
                "stripe_id": subscription_record.stripe_id,
                "status": subscription_record.status,
                "latest_invoice": subscription_record.latest_invoice,
                "identity_id": subscription_record.identity_id,
                "organization_id": subscription_record.organization_id,
                "created_at": subscription_record.created_at,
                "updated_at": subscription_record.updated_at,
            }
            subscription_result.append(subscription_data)

    # Commit all changes
    await session.commit()

    return subscription_result


async def change_org_plan(organization_id: str, code: str, session: AsyncSession) -> str:
    plan = await cruds.subscription_plan.get_by_code(code=code, session=session)
    subscription_create = IOrganizationSubscriptionCreate(
        organization_id=organization_id, plan_id=plan.id, started_at=pendulum.now()
    )
    current_plan = await cruds.organization_subscription.get_active_by_org(org_id=organization_id, db_session=session)
    await cruds.organization_subscription.cancel_subscription(subscription_id=current_plan.id, session=session)
    default_subscription = await cruds.organization_subscription.create(new=subscription_create, db_session=session)
    return default_subscription.id
