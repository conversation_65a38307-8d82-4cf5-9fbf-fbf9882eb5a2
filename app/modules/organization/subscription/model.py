from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING

import sqlalchemy as sa
from sqlmodel import DateTime, Field, Relationship, SQLModel

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ...subscription_plan.model import SubscriptionPlan
    from ..model import Organization


class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class OrganizationSubscriptionBase(SQLModel):
    started_at: datetime = Field(sa_type=DateTime(timezone=True))
    expires_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))
    token_quota: int | None = Field(default=None, sa_type=sa.BigInteger)
    is_trial: bool = Field(default=False)
    status: SubscriptionStatus = Field(
        sa_type=sa.Enum(SubscriptionStatus, native_enum=False, length=50), default=SubscriptionStatus.ACTIVE
    )


class OrganizationSubscription(OrganizationSubscriptionBase, BaseModelMixin, table=True):
    __tablename__ = "organization_subscriptions"
    organization_id: str = Field(foreign_key="organizations.id", nullable=False)
    # Relationships
    organization: "Organization" = Relationship(
        back_populates="subscriptions",
        sa_relationship_kwargs={"foreign_keys": "OrganizationSubscription.organization_id"},
    )
    plan_id: str = Field(foreign_key="subscription_plans.id", nullable=False)
    plan: "SubscriptionPlan" = Relationship(back_populates="organization_subscriptions")
