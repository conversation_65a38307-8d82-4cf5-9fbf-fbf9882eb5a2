from datetime import datetime

from pydantic import BaseModel

from .model import OrganizationSubscriptionBase, SubscriptionStatus


class IOrganizationSubscriptionRead(OrganizationSubscriptionBase):
    plan_id: str
    started_at: datetime
    expires_at: datetime | None
    is_trial: bool
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime


class IOrganizationSubscriptionCreate(BaseModel):
    organization_id: str
    plan_id: str
    started_at: datetime
    expires_at: datetime | None = None
    is_trial: bool = False
    status: SubscriptionStatus = SubscriptionStatus.ACTIVE


class IOrganizationSubscriptionUpdate(BaseModel):
    plan_id: str | None = None
    expires_at: datetime | None = None
    status: SubscriptionStatus | None = None


class ICurrentSubscriptionRead(BaseModel):
    plan_name: str
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime
    expires_at: datetime | None
    started_at: datetime
    token_quota: int
