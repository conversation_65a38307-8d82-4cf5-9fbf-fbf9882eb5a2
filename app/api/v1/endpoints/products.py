from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.modules.stripe_product.models import StripeProduct, StripeProductPrice
from app.modules.stripe_product.schema import (
    StripeProductPriceBase,
    StripeProductWithPricesResponse,
)
from app.services.db.session import new_session

router = APIRouter()


@router.get("/", response_model=IGetResponseBase[dict])
async def list_products(session: AsyncSession = Depends(new_session)):  # noqa: B008
    """List all products with their prices."""
    # Get all products
    products_stmt = select(StripeProduct)
    products_result = await session.execute(products_stmt)
    products = products_result.scalars().all()

    result = []
    for product in products:
        # Get prices for this product
        prices_stmt = select(StripeProductPrice).where(StripeProductPrice.product_id == product.id)
        prices_result = await session.execute(prices_stmt)
        prices = prices_result.scalars().all()

        # Convert to response schema
        product_response = StripeProductWithPricesResponse(
            id=product.id,
            stripe_product_id=product.stripe_product_id,
            name=product.name,
            description=product.description,
            stripe_metadata=product.stripe_metadata,
            active=product.active,
            created_at=product.created_at,
            updated_at=product.updated_at,
            prices=[
                StripeProductPriceBase(
                    stripe_price_id=price.stripe_price_id,
                    product_id=price.product_id,
                    nickname=price.nickname,
                    amount=price.amount,
                    currency=price.currency,
                    active=price.active,
                )
                for price in prices
            ],
        )
        result.append(product_response)
    product_items = {"items": result}

    return create_response(data=product_items, message="Products retrieved successfully")


@router.get("/{product_id}", response_model=IGetResponseBase[StripeProductWithPricesResponse])
async def get_product(product_id: str, session: AsyncSession = Depends(new_session)):  # noqa: B008
    """Get a specific product with its prices."""
    # Get the product
    product_stmt = select(StripeProduct).where(StripeProduct.id == product_id)
    product_result = await session.execute(product_stmt)
    product = product_result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Get prices for this product
    prices_stmt = select(StripeProductPrice).where(StripeProductPrice.product_id == product.id)
    prices_result = await session.execute(prices_stmt)
    prices = prices_result.scalars().all()

    # Convert to response schema
    product_response = StripeProductWithPricesResponse(
        id=product.id,
        stripe_product_id=product.stripe_product_id,
        name=product.name,
        description=product.description,
        stripe_metadata=product.stripe_metadata,
        active=product.active,
        created_at=product.created_at,
        updated_at=product.updated_at,
        prices=[
            StripeProductPriceBase(
                stripe_price_id=price.stripe_price_id,
                product_id=price.product_id,
                nickname=price.nickname,
                amount=price.amount,
                currency=price.currency,
                active=price.active,
            )
            for price in prices
        ],
    )

    return create_response(data=product_response, message="Product retrieved successfully")
